# 交叉编译Linux版本脚本

Write-Host "=== 编译Linux版本 ===" -ForegroundColor Green

# 设置交叉编译环境变量
$env:GOOS = "linux"
$env:GOARCH = "amd64"
$env:CGO_ENABLED = "1"

Write-Host "编译目标: Linux AMD64" -ForegroundColor Yellow
Write-Host "CGO支持: 启用 (SQLite需要)" -ForegroundColor Yellow

# 创建build目录
if (!(Test-Path "build")) {
    New-Item -ItemType Directory -Path "build" -Force | Out-Null
}

try {
    # 编译
    Write-Host "正在编译..." -ForegroundColor Yellow
    go build -o build/cmdmonitor-linux-amd64 cmd/main.go
    
    if (Test-Path "build/cmdmonitor-linux-amd64") {
        Write-Host "Build successful!" -ForegroundColor Green
        
        # 显示文件信息
        $fileInfo = Get-Item "build/cmdmonitor-linux-amd64"
        Write-Host "文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Cyan
        Write-Host "文件路径: $($fileInfo.FullName)" -ForegroundColor Cyan
        
        Write-Host ""
        Write-Host "下一步:" -ForegroundColor Yellow
        Write-Host "1. 上传到Linux服务器:" -ForegroundColor White
        Write-Host "   scp build/cmdmonitor-linux-amd64 user@server:/tmp/" -ForegroundColor Gray
        Write-Host ""
        Write-Host "2. 在服务器上安装:" -ForegroundColor White
        Write-Host "   sudo mv /tmp/cmdmonitor-linux-amd64 /usr/local/bin/cmdmonitor" -ForegroundColor Gray
        Write-Host "   sudo chmod +x /usr/local/bin/cmdmonitor" -ForegroundColor Gray
        Write-Host ""
        Write-Host "3. 或者使用自动部署脚本:" -ForegroundColor White
        Write-Host "   .\deploy.ps1 -Server 'user@server' -EmailUser '<EMAIL>' -EmailPass 'auth_code'" -ForegroundColor Gray
        
    } else {
        Write-Host "Build failed: Output file not found" -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "Build failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "可能的解决方案:" -ForegroundColor Yellow
    Write-Host "1. 确保已安装Go语言环境" -ForegroundColor White
    Write-Host "2. 运行: go mod tidy" -ForegroundColor White
    Write-Host "3. 如果是CGO问题，可以尝试禁用CGO:" -ForegroundColor White
    Write-Host "   `$env:CGO_ENABLED='0'; go build -o build/cmdmonitor-linux-amd64 cmd/main.go" -ForegroundColor Gray
    Write-Host "   (注意: 禁用CGO会无法使用SQLite，需要修改代码使用其他存储)" -ForegroundColor Red
    exit 1
}

# 重置环境变量
Remove-Item Env:GOOS -ErrorAction SilentlyContinue
Remove-Item Env:GOARCH -ErrorAction SilentlyContinue
Remove-Item Env:CGO_ENABLED -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "Build completed successfully!" -ForegroundColor Green
