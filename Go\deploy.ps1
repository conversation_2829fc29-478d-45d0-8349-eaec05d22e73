# Command Monitor 部署脚本 (Windows -> Linux)
# 用法: .\deploy.ps1 -Server "user@your-server" -EmailUser "<EMAIL>" -EmailPass "your_auth_code"

param(
    [Parameter(Mandatory=$true)]
    [string]$Server,
    
    [Parameter(Mandatory=$true)]
    [string]$EmailUser,
    
    [Parameter(Mandatory=$true)]
    [string]$EmailPass,
    
    [string]$EmailHost = "smtp.qq.com",
    [string]$EmailPort = "587",
    [string]$EmailTo = "<EMAIL>"
)

Write-Host "=== Command Monitor 部署脚本 ===" -ForegroundColor Green

# 1. 交叉编译Linux版本
Write-Host "1. 编译Linux版本..." -ForegroundColor Yellow
$env:GOOS = "linux"
$env:GOARCH = "amd64"
$env:CGO_ENABLED = "1"

# 检查是否有Linux交叉编译工具链
try {
    go build -o build/cmdmonitor-linux-amd64 cmd/main.go
    Write-Host "   ✅ 编译成功: build/cmdmonitor-linux-amd64" -ForegroundColor Green
} catch {
    Write-Host "   ❌ 编译失败，可能需要安装交叉编译工具链" -ForegroundColor Red
    Write-Host "   请运行: go install github.com/mattn/go-sqlite3" -ForegroundColor Yellow
    exit 1
}

# 2. 创建配置文件
Write-Host "2. 创建配置文件..." -ForegroundColor Yellow
$configContent = @"
# 邮箱通知配置
EMAIL_SMTP_HOST=$EmailHost
EMAIL_SMTP_PORT=$EmailPort
EMAIL_USERNAME=$EmailUser
EMAIL_PASSWORD=$EmailPass
EMAIL_FROM_ADDRESS=$EmailUser
EMAIL_DEFAULT_TO=$EmailTo

# 监控配置
MONITOR_THRESHOLD_MINUTES=5
SCAN_INTERVAL_SECONDS=30
STORAGE_PATH=/var/lib/cmdmonitor/data.db
LOG_LEVEL=info
LOG_PATH=/var/log/cmdmonitor.log
IGNORE_PROCESSES=systemd,kthreadd,ksoftirqd,migration,rcu_,watchdog
MONITOR_SYSTEM_PROCESSES=false
MAX_MONITORED_PROCESSES=50
MONITOR_DOCKER_ENABLED=false
"@

$configContent | Out-File -FilePath "build/config.env" -Encoding UTF8
Write-Host "   ✅ 配置文件已创建: build/config.env" -ForegroundColor Green

# 3. 创建部署包
Write-Host "3. 创建部署包..." -ForegroundColor Yellow
if (Test-Path "build/deploy") {
    Remove-Item -Recurse -Force "build/deploy"
}
New-Item -ItemType Directory -Path "build/deploy" -Force | Out-Null

# 复制文件到部署目录
Copy-Item "build/cmdmonitor-linux-amd64" "build/deploy/cmdmonitor"
Copy-Item "build/config.env" "build/deploy/"
Copy-Item "scripts/install.sh" "build/deploy/"
Copy-Item "scripts/uninstall.sh" "build/deploy/"
Copy-Item "configs/cmdmonitor.service" "build/deploy/"

Write-Host "   ✅ 部署包已创建: build/deploy/" -ForegroundColor Green

# 4. 上传到服务器
Write-Host "4. 上传到服务器..." -ForegroundColor Yellow
try {
    # 创建临时目录
    ssh $Server "mkdir -p /tmp/cmdmonitor-deploy"
    
    # 上传文件
    scp -r "build/deploy/*" "${Server}:/tmp/cmdmonitor-deploy/"
    
    Write-Host "   ✅ 文件上传成功" -ForegroundColor Green
} catch {
    Write-Host "   ❌ 上传失败，请检查SSH连接" -ForegroundColor Red
    Write-Host "   请确保可以通过SSH连接到服务器: $Server" -ForegroundColor Yellow
    exit 1
}

# 5. 远程安装
Write-Host "5. 远程安装服务..." -ForegroundColor Yellow
$installScript = @'
cd /tmp/cmdmonitor-deploy

# 停止现有服务
sudo systemctl stop cmdmonitor 2>/dev/null || true

# 安装二进制文件
sudo cp cmdmonitor /usr/local/bin/
sudo chmod +x /usr/local/bin/cmdmonitor

# 创建目录
sudo mkdir -p /etc/cmdmonitor
sudo mkdir -p /var/lib/cmdmonitor
sudo mkdir -p /var/log

# 安装配置文件
sudo cp config.env /etc/cmdmonitor/
sudo chmod 600 /etc/cmdmonitor/config.env

# 安装服务文件
sudo cp cmdmonitor.service /etc/systemd/system/
sudo systemctl daemon-reload

# 启动服务
sudo systemctl enable cmdmonitor
sudo systemctl start cmdmonitor

# 检查状态
sleep 2
if sudo systemctl is-active --quiet cmdmonitor; then
    echo "✅ 服务启动成功"
    sudo systemctl status cmdmonitor --no-pager -l
else
    echo "❌ 服务启动失败"
    sudo journalctl -u cmdmonitor --no-pager -l
    exit 1
fi

# 清理临时文件
rm -rf /tmp/cmdmonitor-deploy

echo "🎉 部署完成！"
echo "查看日志: sudo journalctl -u cmdmonitor -f"
echo "查看状态: sudo systemctl status cmdmonitor"
'@

try {
    $installScript | ssh $Server "bash"
    Write-Host "   ✅ 远程安装成功" -ForegroundColor Green
} catch {
    Write-Host "   ❌ 远程安装失败" -ForegroundColor Red
    exit 1
}

# 6. 验证部署
Write-Host "6. 验证部署..." -ForegroundColor Yellow
try {
    $status = ssh $Server "sudo systemctl is-active cmdmonitor"
    if ($status -eq "active") {
        Write-Host "   ✅ 服务运行正常" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  服务状态: $status" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ 无法获取服务状态" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 部署完成！" -ForegroundColor Green
Write-Host ""
Write-Host "常用命令:" -ForegroundColor Cyan
Write-Host "  查看状态: ssh $Server 'sudo systemctl status cmdmonitor'" -ForegroundColor White
Write-Host "  查看日志: ssh $Server 'sudo journalctl -u cmdmonitor -f'" -ForegroundColor White
Write-Host "  重启服务: ssh $Server 'sudo systemctl restart cmdmonitor'" -ForegroundColor White
Write-Host "  停止服务: ssh $Server 'sudo systemctl stop cmdmonitor'" -ForegroundColor White
Write-Host ""
Write-Host "配置文件位置: /etc/cmdmonitor/config.env" -ForegroundColor Cyan
Write-Host "日志文件位置: /var/log/cmdmonitor.log" -ForegroundColor Cyan
