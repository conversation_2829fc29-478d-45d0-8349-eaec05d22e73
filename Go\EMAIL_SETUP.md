# 邮箱通知配置指南

Command Monitor 支持通过SMTP发送邮件通知。以下是常见邮箱服务商的配置方法。

## 🎯 快速配置

### 环境变量配置
```bash
export EMAIL_SMTP_HOST="smtp.qq.com"
export EMAIL_SMTP_PORT="587"
export EMAIL_USERNAME="<EMAIL>"
export EMAIL_PASSWORD="your_app_password"
export EMAIL_FROM_ADDRESS="<EMAIL>"
export EMAIL_DEFAULT_TO="<EMAIL>"
```

## 📧 常见邮箱服务商配置

### QQ邮箱
```bash
EMAIL_SMTP_HOST=smtp.qq.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_authorization_code  # 不是QQ密码，是授权码
EMAIL_FROM_ADDRESS=<EMAIL>
```

**获取QQ邮箱授权码步骤**：
1. 登录QQ邮箱网页版
2. 点击"设置" → "账户"
3. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
4. 开启"IMAP/SMTP服务"
5. 按提示发送短信，获取授权码
6. 将授权码作为 `EMAIL_PASSWORD` 使用

### 163邮箱
```bash
EMAIL_SMTP_HOST=smtp.163.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_client_password  # 客户端授权密码
EMAIL_FROM_ADDRESS=<EMAIL>
```

### Gmail
```bash
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password  # 应用专用密码
EMAIL_FROM_ADDRESS=<EMAIL>
```

### 企业邮箱
```bash
EMAIL_SMTP_HOST=smtp.exmail.qq.com  # 腾讯企业邮箱
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_password
EMAIL_FROM_ADDRESS=<EMAIL>
```

## 🔧 配置文件方式

创建 `/etc/cmdmonitor/config.env` 文件：
```bash
# 邮箱通知配置
EMAIL_SMTP_HOST=smtp.qq.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_authorization_code
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_DEFAULT_TO=<EMAIL>

# 其他配置...
MONITOR_THRESHOLD_MINUTES=5
SCAN_INTERVAL_SECONDS=30
```

## 🧪 测试邮件配置

### 方法1: 使用测试脚本
```bash
# 设置环境变量后运行
./test_run.ps1
```

### 方法2: 手动测试
```bash
# 设置环境变量
export EMAIL_SMTP_HOST="smtp.qq.com"
export EMAIL_SMTP_PORT="587"
export EMAIL_USERNAME="<EMAIL>"
export EMAIL_PASSWORD="your_authorization_code"
export EMAIL_FROM_ADDRESS="<EMAIL>"
export EMAIL_DEFAULT_TO="<EMAIL>"

# 运行程序（会自动发送测试邮件）
./build/cmdmonitor
```

## 🚨 常见问题

### 1. 认证失败
**错误**: `535 Authentication failed`
**解决**: 
- 检查用户名和密码是否正确
- 确认使用的是授权码而不是登录密码
- 确认已开启SMTP服务

### 2. 连接超时
**错误**: `dial tcp: i/o timeout`
**解决**:
- 检查SMTP服务器地址和端口
- 确认网络连接正常
- 检查防火墙设置

### 3. TLS错误
**错误**: `tls: handshake failure`
**解决**:
- 确认使用正确的端口（通常587支持STARTTLS）
- 检查SMTP服务器是否支持TLS

### 4. 发件人验证失败
**错误**: `550 Invalid sender`
**解决**:
- 确认 `EMAIL_FROM_ADDRESS` 与 `EMAIL_USERNAME` 一致
- 检查邮箱是否已验证

## 📋 配置检查清单

- [ ] SMTP服务器地址正确
- [ ] SMTP端口正确（通常是587或465）
- [ ] 用户名格式正确（通常是完整邮箱地址）
- [ ] 使用授权码而不是登录密码
- [ ] 发件地址与用户名一致
- [ ] 收件地址格式正确
- [ ] 网络连接正常
- [ ] 邮箱服务商SMTP服务已开启

## 🔐 安全建议

1. **使用授权码**: 不要使用邮箱登录密码，使用专门的授权码或应用密码
2. **环境变量**: 敏感信息通过环境变量传递，不要硬编码在代码中
3. **权限控制**: 配置文件设置适当的文件权限（600）
4. **定期更换**: 定期更换授权码

## 📞 技术支持

如果遇到配置问题：
1. 检查日志文件：`/var/log/cmdmonitor.log`
2. 查看系统日志：`journalctl -u cmdmonitor -f`
3. 验证配置：确认所有必需的环境变量都已设置
4. 测试连接：使用telnet测试SMTP服务器连接

---

**默认收件人**: <EMAIL>  
**推荐邮箱**: QQ邮箱（配置简单，稳定可靠）
