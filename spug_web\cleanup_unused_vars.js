const fs = require('fs');
const path = require('path');

// 需要清理的文件和对应的未使用变量
const cleanupTasks = [
  {
    file: 'src/pages/exec/result-extraction/TemplateSelector.js',
    removals: [
      { type: 'import', name: 'But<PERSON>' },
      { type: 'variable', name: 'searchText', line: 100 }
    ]
  },
  {
    file: 'src/pages/host/gpu-dashboard/components/GpuDriverInfo.js',
    removals: [
      { type: 'import', name: 'SettingOutlined' }
    ]
  },
  {
    file: 'src/pages/host/Group.js',
    removals: [
      { type: 'import', name: 'QuestionCircleOutlined' }
    ]
  },
  {
    file: 'src/pages/exec/task/Output.js',
    removals: [
      { type: 'variable', name: 'gStore' }
    ]
  },
  {
    file: 'src/components/UserSelector.js',
    removals: [
      { type: 'import', name: 'useEffect' }
    ]
  },
  {
    file: 'src/components/DragUpload/index.js',
    removals: [
      { type: 'variable', name: 'uploadRef' }
    ]
  },
  {
    file: 'src/pages/model-storage/WeChatConfigModal.js',
    removals: [
      { type: 'import', name: 'Space' },
      { type: 'import', name: 'Divider' }
    ]
  }
];

function cleanupFile(filePath, removals) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(fullPath, 'utf8');
  let modified = false;
  
  removals.forEach(removal => {
    if (removal.type === 'import') {
      // 移除导入中的特定项
      const importRegex = new RegExp(`,\\s*${removal.name}\\s*(?=,|})`, 'g');
      const importRegex2 = new RegExp(`{\\s*${removal.name}\\s*,`, 'g');
      const importRegex3 = new RegExp(`{\\s*${removal.name}\\s*}`, 'g');
      
      if (content.match(importRegex) || content.match(importRegex2) || content.match(importRegex3)) {
        content = content.replace(importRegex, '');
        content = content.replace(importRegex2, '{');
        content = content.replace(importRegex3, '{}');
        modified = true;
      }
    } else if (removal.type === 'variable') {
      // 移除变量声明
      const varRegex = new RegExp(`\\s*const\\s+${removal.name}\\s*=.*?;`, 'g');
      const varRegex2 = new RegExp(`\\s*const\\s+\\[.*?${removal.name}.*?\\]\\s*=.*?;`, 'g');
      
      if (content.match(varRegex) || content.match(varRegex2)) {
        content = content.replace(varRegex, '');
        content = content.replace(varRegex2, '');
        modified = true;
      }
    }
  });
  
  if (modified) {
    fs.writeFileSync(fullPath, content);
    console.log(`Cleaned up: ${filePath}`);
  }
}

// 执行清理
cleanupTasks.forEach(task => {
  cleanupFile(task.file, task.removals);
});

console.log('Cleanup completed!');
