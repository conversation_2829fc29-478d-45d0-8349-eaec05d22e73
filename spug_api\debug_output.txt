=== 测试API逻辑 ===
数据库中共有 1 条记录
ID: 1
plan_name: '测试计划'
task_name: '测试任务'
source_type: execution
metrics: [{"label": "AverageUserSpeed", "value": "30.283630", "unit": "GB/s", "confidence": 0.98}, {"label": "AveragePCIeSpeed", "value": "30.429640", "unit": "GB/s", "confidence": 0.98}, {"label": "AverageUserSpeed", "value": "54.126707", "unit": "GB/s", "confidence": 0.98}, {"label": "AveragePCIeSpeed", "value": "54.581893", "unit": "GB/s", "confidence": 0.98}]
---
to_dict() 成功: {
  "id": 1,
  "execution_id": 2,
  "plan_name": "测试计划",
  "task_name": "测试任务",
  "source_type": "execution",
  "log_source_host": null,
  "log_source_path": null,
  "log_extraction_method": null,
  "metrics": [
    {
      "label": "AverageUserSpeed",
      "value": "30.283630",
      "unit": "GB/s",
      "confidence": 0.98
    },
    {
      "label": "AveragePCIeSpeed",
      "value": "30.429640",
      "unit": "GB/s",
      "confidence": 0.98
    },
    {
      "label": "AverageUserSpeed",
      "value": "54.126707",
      "unit": "GB/s",
      "confidence": 0.98
    },
    {
      "label": "AveragePCIeSpeed",
      "value": "54.581893",
      "unit": "GB/s",
      "confidence": 0.98
    }
  ],
  "raw_log": "",
  "total_metrics": 4,
  "confirmed_metrics": 4,
  "ai_confidence": 0.0,
  "success_rate": 100.0,
  "is_standalone": false,
  "created_at": "2025-07-08 14:25:15",
  "updated_at": "2025-07-16 17:22:53",
  "created_by_id": 1
}
==================================================
json_response 测试成功: <HttpResponse status_code=200, "application/json; charset=utf-8">
