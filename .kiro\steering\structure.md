# Project Structure

## Root Directory Layout

```
spug/
├── spug_api/           # Django backend application
├── spug_web/           # React frontend application
├── docs/               # Documentation (API, user manuals, development)
├── config/             # Configuration files (funboost, logging)
├── assets/             # Static resources (screenshots, images)
├── temp/               # Temporary files and test data
├── logs/               # Runtime logs (auto-generated)
├── start.ps1           # Windows startup script
└── README.md           # Project documentation
```

## Backend Structure (spug_api/)

```
spug_api/
├── apps/               # Django applications
├── spug/               # Main Django project settings
├── consumer/           # Task consumers and workers
├── libs/               # Shared libraries and utilities
├── tools/              # Helper scripts and tools
├── files/              # File storage
├── repos/              # Repository management
├── storage/            # Data storage
├── manage.py           # Django management script
├── requirements.txt    # Python dependencies
└── venv/               # Virtual environment (created at runtime)
```

## Frontend Structure (spug_web/)

```
spug_web/
├── src/                # React source code
├── public/             # Static assets
├── build/              # Production build output
├── node_modules/       # NPM dependencies (created at runtime)
├── package.json        # NPM configuration
└── config-overrides.js # Webpack customization
```

## Documentation Structure (docs/)

```
docs/
├── api/                # API documentation
├── user-manual/        # End-user guides (HTML format)
├── development/        # Developer documentation
├── docker/             # Docker deployment files
├── FQA.md              # Frequently asked questions
└── install.sh          # Installation script
```

## Key Conventions

### File Organization

- **Configuration**: All config files in `/config/` directory
- **Documentation**: Organized by audience (api, user-manual, development)
- **Assets**: Screenshots and images in `/assets/` for documentation
- **Temporary Files**: Use `/temp/` for test files and temporary data
- **Logs**: Auto-generated in `/logs/` with date-based naming

### Naming Patterns

- **Log Files**: `YYYY-MM-DD.NNNN.module.log` format
- **Config Files**: `*_config.py` suffix for Python configurations
- **Documentation**: Use descriptive names, support both Chinese and English
- **Assets**: Descriptive Chinese names for screenshots

### Code Organization

- **Backend**: Follow Django app structure with clear separation of concerns
- **Frontend**: Component-based React architecture with MobX state management
- **Shared**: Common utilities in `libs/` and `tools/` directories

### Development Workflow

- **Environment**: Use provided `start.ps1` for consistent setup
- **Dependencies**: Lock versions in requirements.txt and package.json
- **Logs**: Structured logging with task IDs for traceability
- **Testing**: Test files in `/temp/` directory, separate from production code

### Maintenance Guidelines

- **Cleanup**: Regularly clean `/temp/` and `/logs/` directories
- **Updates**: Update documentation when adding new features
- **Configuration**: Keep environment-specific configs in `/config/`
- **Assets**: Add new screenshots to `/assets/` with descriptive names
