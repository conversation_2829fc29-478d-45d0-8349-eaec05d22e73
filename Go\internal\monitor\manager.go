package monitor

import (
	"cmdmonitor/pkg/utils"
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// MonitorManager 监控管理器
type MonitorManager struct {
	logger         *logrus.Logger
	processScanner *ProcessScanner
	processes      map[int]*utils.MonitoredProcess
	mutex          sync.RWMutex
	scanInterval   time.Duration
	maxProcesses   int

	// 回调函数
	onProcessCompleted func(*utils.MonitoredProcess)

	// 控制通道
	stopChan chan struct{}
	doneChan chan struct{}
}

// NewMonitorManager 创建新的监控管理器
func NewMonitorManager(
	logger *logrus.Logger,
	processScanner *ProcessScanner,
	scanIntervalSeconds int,
	maxProcesses int,
) *MonitorManager {
	return &MonitorManager{
		logger:         logger,
		processScanner: processScanner,
		processes:      make(map[int]*utils.MonitoredProcess),
		scanInterval:   time.Duration(scanIntervalSeconds) * time.Second,
		maxProcesses:   maxProcesses,
		stopChan:       make(chan struct{}),
		done<PERSON>han:       make(chan struct{}),
	}
}

// SetProcessCompletedCallback 设置进程完成回调
func (mm *MonitorManager) SetProcessCompletedCallback(callback func(*utils.MonitoredProcess)) {
	mm.onProcessCompleted = callback
}

// Start 启动监控管理器
func (mm *MonitorManager) Start(ctx context.Context) error {
	mm.logger.Info("启动监控管理器...")

	go mm.monitorLoop(ctx)

	mm.logger.Info("监控管理器已启动")
	return nil
}

// Stop 停止监控管理器
func (mm *MonitorManager) Stop() {
	mm.logger.Info("停止监控管理器...")
	close(mm.stopChan)
	<-mm.doneChan
	mm.logger.Info("监控管理器已停止")
}

// monitorLoop 主监控循环
func (mm *MonitorManager) monitorLoop(ctx context.Context) {
	defer close(mm.doneChan)

	ticker := time.NewTicker(mm.scanInterval)
	defer ticker.Stop()

	// 立即执行一次扫描
	mm.scanAndUpdate()

	for {
		select {
		case <-ctx.Done():
			mm.logger.Info("收到上下文取消信号，停止监控")
			return
		case <-mm.stopChan:
			mm.logger.Info("收到停止信号，停止监控")
			return
		case <-ticker.C:
			mm.scanAndUpdate()
		}
	}
}

// scanAndUpdate 扫描并更新进程状态
func (mm *MonitorManager) scanAndUpdate() {
	mm.logger.Debug("开始扫描和更新进程状态...")

	// 1. 检查现有监控进程的状态
	mm.checkExistingProcesses()

	// 2. 扫描新的长时间运行进程
	mm.scanNewProcesses()

	// 3. 清理已完成的进程
	mm.cleanupCompletedProcesses()

	mm.mutex.RLock()
	activeCount := len(mm.processes)
	mm.mutex.RUnlock()

	mm.logger.Debugf("当前监控 %d 个进程", activeCount)
}

// checkExistingProcesses 检查现有监控进程的状态
func (mm *MonitorManager) checkExistingProcesses() {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	for pid, monitoredProc := range mm.processes {
		if monitoredProc.Status != "running" {
			continue
		}

		// 检查进程是否还在运行
		if !utils.IsProcessRunning(pid) {
			mm.logger.Infof("进程 %d (%s) 已结束", pid, monitoredProc.Info.Command)

			// 更新进程状态
			monitoredProc.Status = "completed"
			monitoredProc.Duration = time.Since(monitoredProc.StartTime)

			// 尝试获取退出码（在某些情况下可能无法获取）
			exitCode := 0 // 默认成功
			monitoredProc.ExitCode = &exitCode

			// 触发完成回调
			if mm.onProcessCompleted != nil {
				go mm.onProcessCompleted(monitoredProc)
			}
		} else {
			// 更新进程信息
			mm.updateProcessInfo(monitoredProc)
		}
	}
}

// updateProcessInfo 更新进程信息
func (mm *MonitorManager) updateProcessInfo(monitoredProc *utils.MonitoredProcess) {
	// 刷新进程信息
	if refreshedInfo, err := mm.processScanner.RefreshProcessInfo(monitoredProc.Info.PID); err == nil {
		// 更新内存和CPU使用情况
		monitoredProc.Info.MemoryUsage = refreshedInfo.MemoryUsage
		monitoredProc.Info.CPUTime = refreshedInfo.CPUTime
		monitoredProc.MemoryUsage = refreshedInfo.MemoryUsage

		// 计算CPU使用率（简化版本）
		monitoredProc.CPUUsage = mm.calculateCPUUsage(refreshedInfo.CPUTime, monitoredProc.StartTime)

		monitoredProc.LastSeen = time.Now()
	}
}

// calculateCPUUsage 计算CPU使用率
func (mm *MonitorManager) calculateCPUUsage(cpuTime int64, startTime time.Time) float64 {
	// 简化的CPU使用率计算
	// 实际实现可能需要更复杂的逻辑
	runningTime := time.Since(startTime).Seconds()
	if runningTime == 0 {
		return 0
	}

	// 假设时钟频率为100Hz
	cpuSeconds := float64(cpuTime) / 100.0
	return (cpuSeconds / runningTime) * 100.0
}

// scanNewProcesses 扫描新的长时间运行进程
func (mm *MonitorManager) scanNewProcesses() {
	// 检查是否已达到最大监控数量
	mm.mutex.RLock()
	currentCount := len(mm.processes)
	mm.mutex.RUnlock()

	if currentCount >= mm.maxProcesses {
		mm.logger.Debugf("已达到最大监控进程数 (%d)，跳过新进程扫描", mm.maxProcesses)
		return
	}

	// 扫描主机进程
	hostProcesses, err := mm.processScanner.ScanProcesses()
	if err != nil {
		mm.logger.Errorf("扫描主机进程失败: %v", err)
	} else {
		mm.addNewProcesses(hostProcesses, false, "")
	}
}

// addNewProcesses 添加新发现的进程到监控列表
func (mm *MonitorManager) addNewProcesses(processes []utils.ProcessInfo, isContainer bool, containerID string) {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	for _, procInfo := range processes {
		// 检查是否已经在监控中
		if _, exists := mm.processes[procInfo.PID]; exists {
			continue
		}

		// 检查是否已达到最大监控数量
		if len(mm.processes) >= mm.maxProcesses {
			mm.logger.Warnf("已达到最大监控进程数 (%d)，停止添加新进程", mm.maxProcesses)
			break
		}

		// 创建监控进程
		monitoredProc := &utils.MonitoredProcess{
			Info:        procInfo,
			StartTime:   procInfo.StartTime,
			IsContainer: isContainer,
			ContainerID: containerID,
			Status:      "running",
			Duration:    time.Since(procInfo.StartTime),
			CPUUsage:    mm.calculateCPUUsage(procInfo.CPUTime, procInfo.StartTime),
			MemoryUsage: procInfo.MemoryUsage,
			LastSeen:    time.Now(),
		}

		mm.processes[procInfo.PID] = monitoredProc

		mm.logger.Infof("开始监控进程: %s (PID=%d, 运行时间=%s)",
			procInfo.Command, procInfo.PID, utils.FormatDuration(time.Since(procInfo.StartTime)))
	}
}

// cleanupCompletedProcesses 清理已完成的进程
func (mm *MonitorManager) cleanupCompletedProcesses() {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	// 清理已完成且超过保留时间的进程
	retentionTime := 1 * time.Hour
	now := time.Now()

	for pid, proc := range mm.processes {
		if proc.Status == "completed" {
			// 如果完成时间超过保留时间，则删除
			if now.Sub(proc.LastSeen) > retentionTime {
				mm.logger.Debugf("清理已完成进程: %s (PID=%d)", proc.Info.Command, pid)
				delete(mm.processes, pid)
			}
		}
	}
}

// GetAllProcesses 获取所有监控的进程
func (mm *MonitorManager) GetAllProcesses() []*utils.MonitoredProcess {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	processes := make([]*utils.MonitoredProcess, 0, len(mm.processes))
	for _, proc := range mm.processes {
		processes = append(processes, proc)
	}

	return processes
}

// GetProcess 获取指定PID的进程
func (mm *MonitorManager) GetProcess(pid int) (*utils.MonitoredProcess, error) {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	if proc, exists := mm.processes[pid]; exists {
		return proc, nil
	}

	return nil, fmt.Errorf("进程 %d 未被监控", pid)
}

// GetActiveProcessCount 获取活跃进程数量
func (mm *MonitorManager) GetActiveProcessCount() int {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	count := 0
	for _, proc := range mm.processes {
		if proc.Status == "running" {
			count++
		}
	}

	return count
}

// GetCompletedProcessCount 获取已完成进程数量
func (mm *MonitorManager) GetCompletedProcessCount() int {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	count := 0
	for _, proc := range mm.processes {
		if proc.Status == "completed" {
			count++
		}
	}

	return count
}
