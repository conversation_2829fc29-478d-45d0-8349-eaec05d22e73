import React, { useState } from 'react';
import { Modal, Card, Row, Col, Select, Tabs, Empty } from 'antd';
import { LineChart, Line, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import store from './store';
import styles from './ChartView.module.less';

const { TabPane } = Tabs;

export default function ChartView({ visible, onCancel, data }) {
  const [chartType] = useState('trend'); // trend | plan | task | metrics

  // 颜色配置
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  // 趋势分析数据
  const getTrendData = () => {
    return store.timeSeriesData;
  };

  // 按计划分组数据
  const getPlanData = () => {
    return store.resultsByPlan.slice(0, 10); // 只显示前10个
  };

  // 按任务分组数据
  const getTaskData = () => {
    return store.resultsByTask.slice(0, 10); // 只显示前10个
  };

  // 指标分布数据
  const getMetricsDistribution = () => {
    const distribution = {};
    data.forEach(result => {
      const rate = result.success_rate || 0;
      let range;
      if (rate >= 90) range = '90-100%';
      else if (rate >= 80) range = '80-90%';
      else if (rate >= 60) range = '60-80%';
      else if (rate >= 40) range = '40-60%';
      else range = '0-40%';
      
      distribution[range] = (distribution[range] || 0) + 1;
    });

    return Object.entries(distribution).map(([range, count]) => ({
      range,
      count,
      percentage: ((count / data.length) * 100).toFixed(1)
    }));
  };

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className={styles.customTooltip}>
          <p className={styles.tooltipLabel}>{label}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {entry.value}
              {entry.name.includes('率') ? '%' : ''}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // 趋势图表
  const renderTrendChart = () => {
    const trendData = getTrendData();
    if (trendData.length === 0) {
      return <Empty description="暂无趋势数据" />;
    }

    return (
      <Card title="成功率和置信度趋势">
        <ResponsiveContainer width="100%" height={400}>
          <LineChart data={trendData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="avgSuccessRate" 
              stroke="#8884d8" 
              name="平均成功率"
              strokeWidth={2}
            />
            <Line 
              type="monotone" 
              dataKey="avgConfidence" 
              stroke="#82ca9d" 
              name="平均AI置信度"
              strokeWidth={2}
            />
          </LineChart>
        </ResponsiveContainer>
      </Card>
    );
  };

  // 计划对比图表
  const renderPlanChart = () => {
    const planData = getPlanData();
    if (planData.length === 0) {
      return <Empty description="暂无计划数据" />;
    }

    return (
      <Card title="测试计划性能对比">
        <ResponsiveContainer width="100%" height={400}>
          <BarChart data={planData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="planName" angle={-45} textAnchor="end" height={100} />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar dataKey="avgSuccessRate" fill="#8884d8" name="平均成功率" />
            <Bar dataKey="count" fill="#82ca9d" name="执行次数" />
          </BarChart>
        </ResponsiveContainer>
      </Card>
    );
  };

  // 任务对比图表
  const renderTaskChart = () => {
    const taskData = getTaskData();
    if (taskData.length === 0) {
      return <Empty description="暂无任务数据" />;
    }

    return (
      <Card title="任务性能对比">
        <ResponsiveContainer width="100%" height={400}>
          <BarChart data={taskData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="taskName" angle={-45} textAnchor="end" height={100} />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar dataKey="avgSuccessRate" fill="#0088FE" name="平均成功率" />
            <Bar dataKey="confirmedMetrics" fill="#00C49F" name="确认指标数" />
          </BarChart>
        </ResponsiveContainer>
      </Card>
    );
  };

  // 指标分布饼图
  const renderMetricsChart = () => {
    const distributionData = getMetricsDistribution();
    if (distributionData.length === 0) {
      return <Empty description="暂无分布数据" />;
    }

    return (
      <Row gutter={16}>
        <Col span={12}>
          <Card title="成功率分布">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={distributionData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ range, percentage }) => `${range} (${percentage}%)`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {distributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="成功率统计">
            <div className={styles.statsContainer}>
              {distributionData.map((item, index) => (
                <div key={index} className={styles.statItem}>
                  <div 
                    className={styles.colorIndicator}
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  />
                  <span className={styles.statLabel}>{item.range}</span>
                  <span className={styles.statValue}>{item.count} 个</span>
                  <span className={styles.statPercentage}>({item.percentage}%)</span>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>
    );
  };

  // 综合报告
  const renderSummaryReport = () => {
    const totalResults = data.length;
    const avgSuccess = store.avgSuccessRate;
    const avgConfidence = store.avgAIConfidence;
    const totalConfirmed = store.totalConfirmedMetrics;
    const planCount = store.resultsByPlan.length;
    const taskCount = store.resultsByTask.length;

    return (
      <Card title="综合分析报告">
        <div className={styles.reportContent}>
          <Row gutter={16}>
            <Col span={8}>
              <div className={styles.reportSection}>
                <h4>数据概览</h4>
                <ul>
                  <li>总测试结果: <strong>{totalResults}</strong> 个</li>
                  <li>覆盖测试计划: <strong>{planCount}</strong> 个</li>
                  <li>涉及任务: <strong>{taskCount}</strong> 个</li>
                  <li>已确认指标: <strong>{totalConfirmed}</strong> 个</li>
                </ul>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles.reportSection}>
                <h4>性能指标</h4>
                <ul>
                  <li>平均成功率: <strong>{avgSuccess}%</strong></li>
                  <li>平均AI置信度: <strong>{avgConfidence}%</strong></li>
                  <li>
                    整体评级: 
                    <strong style={{ 
                      color: avgSuccess >= 80 ? '#52c41a' : avgSuccess >= 60 ? '#faad14' : '#ff4d4f' 
                    }}>
                      {avgSuccess >= 80 ? '优秀' : avgSuccess >= 60 ? '良好' : '需改进'}
                    </strong>
                  </li>
                </ul>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles.reportSection}>
                <h4>建议</h4>
                <ul>
                  {avgSuccess < 60 && <li>建议优化测试流程，提高成功率</li>}
                  {avgConfidence < 60 && <li>建议增加人工确认，提高数据质量</li>}
                  {planCount > 10 && <li>测试计划较多，建议分类管理</li>}
                  {totalConfirmed / (data.reduce((sum, d) => sum + (d.total_metrics || 0), 0)) < 0.8 && 
                    <li>建议加强指标确认工作</li>}
                </ul>
              </div>
            </Col>
          </Row>
        </div>
      </Card>
    );
  };

  if (!data || data.length === 0) {
    return (
      <Modal
        title="图表分析"
        visible={visible}
        onCancel={onCancel}
        width={1200}
        footer={null}
      >
        <Empty description="暂无数据可用于分析" />
      </Modal>
    );
  }

  return (
    <Modal
      title="图表分析"
      visible={visible}
      onCancel={onCancel}
      width={1200}
      footer={null}
      bodyStyle={{ padding: '16px' }}
    >
      <div className={styles.chartContainer}>
        <Tabs defaultActiveKey="trend" size="large">
          <TabPane tab="趋势分析" key="trend">
            {renderTrendChart()}
          </TabPane>
          <TabPane tab="计划对比" key="plan">
            {renderPlanChart()}
          </TabPane>
          <TabPane tab="任务对比" key="task">
            {renderTaskChart()}
          </TabPane>
          <TabPane tab="分布统计" key="metrics">
            {renderMetricsChart()}
          </TabPane>
          <TabPane tab="综合报告" key="summary">
            {renderSummaryReport()}
          </TabPane>
        </Tabs>
      </div>
    </Modal>
  );
} 