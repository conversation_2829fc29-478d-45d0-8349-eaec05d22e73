[Unit]
Description=Command Monitor Service - 长命令监控服务
Documentation=https://github.com/your-org/cmdmonitor
After=network.target docker.service
Wants=docker.service

[Service]
Type=simple
User=root
Group=root
ExecStart=/usr/local/bin/cmdmonitor
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
TimeoutStopSec=30

# 环境变量配置文件
EnvironmentFile=-/etc/cmdmonitor/config.env

# 安全配置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/lib/cmdmonitor /var/log

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=cmdmonitor

[Install]
WantedBy=multi-user.target
