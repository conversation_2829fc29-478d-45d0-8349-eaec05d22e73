# 开发者后门功能文档

## 概述

开发者后门是为了方便开发和测试而设计的特殊认证机制。通过使用特定的token，开发者可以绕过正常的用户认证流程，直接访问所有API接口。

## ⚠️ 安全警告

**🚫 此功能仅用于开发和测试环境，生产环境必须禁用！**

## 配置说明

### 启用/禁用后门

在 `spug_api/spug/settings.py` 中配置：

```python
# 🚪 开发者后门配置 - 生产环境请设置为False
ENABLE_DEVELOPER_BACKDOOR = True  # 是否启用后门
DEVELOPER_BACKDOOR_TOKEN = "1"    # 后门token
```

### 环境配置建议

```python
# 开发环境
ENABLE_DEVELOPER_BACKDOOR = True
DEVELOPER_BACKDOOR_TOKEN = "dev_token_123"

# 测试环境  
ENABLE_DEVELOPER_BACKDOOR = True
DEVELOPER_BACKDOOR_TOKEN = "test_token_456"

# 生产环境
ENABLE_DEVELOPER_BACKDOOR = False  # 必须禁用
DEVELOPER_BACKDOOR_TOKEN = ""      # 清空token
```

## 使用方法

### 1. HTTP请求头方式

```bash
curl -H "X-Token: 1" http://localhost:8000/api/model-storage/new-release-plans/
```

### 2. URL参数方式

```bash
curl "http://localhost:8000/api/model-storage/new-release-plans/?x-token=1"
```

### 3. JavaScript/Axios示例

```javascript
// 使用axios
const response = await axios.get('/api/model-storage/new-release-plans/', {
  headers: {
    'X-Token': '1'
  }
});

// 使用fetch
const response = await fetch('/api/model-storage/new-release-plans/', {
  headers: {
    'X-Token': '1'
  }
});
```

### 4. Python requests示例

```python
import requests

headers = {'X-Token': '1'}
response = requests.get('http://localhost:8000/api/model-storage/new-release-plans/', headers=headers)
print(response.json())
```

## API测试示例

### 获取数据

```bash
# 获取发布计划列表
curl -H "X-Token: 1" http://localhost:8000/api/model-storage/new-release-plans/

# 获取测试任务列表
curl -H "X-Token: 1" http://localhost:8000/api/model-storage/test-tasks/

# 获取GPU设备列表
curl -H "X-Token: 1" http://localhost:8000/api/model-storage/gpu-devices/

# 获取服务器监控指标
curl -H "X-Token: 1" http://localhost:8000/api/model-storage/server-metrics/
```

### 创建数据

```bash
# 创建发布计划
curl -X POST -H "X-Token: 1" -H "Content-Type: application/json" \
  -d '{
    "name": "测试发布计划",
    "description": "通过后门创建的测试计划",
    "start_date": "2025-01-20",
    "end_date": "2025-02-20",
    "status": "planning",
    "risk_level": "low",
    "created_by": "developer"
  }' \
  http://localhost:8000/api/model-storage/new-release-plans/

# 创建测试任务
curl -X POST -H "X-Token: 1" -H "Content-Type: application/json" \
  -d '{
    "model_name": "测试模型",
    "tester": "开发者",
    "model_type": "inference",
    "gpu_model": "P800",
    "start_date": "2025-01-20",
    "end_date": "2025-01-30",
    "priority": "p2",
    "test_status": "pending"
  }' \
  http://localhost:8000/api/model-storage/test-tasks/
```

## 测试工具

### 1. 快速测试脚本

```bash
# 运行基础测试
python temp/test_developer_backdoor.py
```

### 2. 完整管理工具

```bash
# 运行管理工具
python temp/developer_backdoor_manager.py
```

管理工具功能：
- ✅ 检查后门配置状态
- 🧪 测试后门功能
- 📝 创建测试数据
- 📋 生成curl使用示例
- ⚠️ 显示安全警告

## 技术实现

### 中间件实现

开发者后门在 `libs/middleware.py` 的 `AuthenticationMiddleware` 中实现：

```python
class DeveloperUser:
    """开发者后门用户类"""
    def __init__(self):
        self.id = -1
        self.username = 'developer'
        self.nickname = '开发者'
        self.is_supper = True
        self.is_active = True
        self.type = 'developer'
        
    def has_perms(self, codes):
        """开发者拥有所有权限"""
        return True

# 在认证中间件中检查后门token
if (getattr(settings, 'ENABLE_DEVELOPER_BACKDOOR', False) and 
    access_token == getattr(settings, 'DEVELOPER_BACKDOOR_TOKEN', '1')):
    request.user = DeveloperUser()
    return None
```

### 权限机制

开发者后门用户具有以下特点：
- 🆔 用户ID: -1 (虚拟用户)
- 👤 用户名: developer
- 🔑 权限: 拥有所有权限 (`has_perms` 始终返回 `True`)
- 🏷️ 类型: developer
- ✅ 状态: 始终活跃

## 安全考虑

### 1. 环境隔离

```python
# 使用环境变量控制
import os

ENABLE_DEVELOPER_BACKDOOR = os.getenv('ENABLE_DEV_BACKDOOR', 'False').lower() == 'true'
DEVELOPER_BACKDOOR_TOKEN = os.getenv('DEV_BACKDOOR_TOKEN', '')
```

### 2. IP限制（可选）

```python
# 在中间件中添加IP限制
ALLOWED_DEV_IPS = ['127.0.0.1', '***********/24']

def is_dev_ip_allowed(request):
    client_ip = get_request_real_ip(request.headers)
    # 检查IP是否在允许列表中
    return client_ip in ALLOWED_DEV_IPS
```

### 3. 时间限制（可选）

```python
# 添加时间窗口限制
import time

DEV_BACKDOOR_START_TIME = "09:00"  # 上午9点
DEV_BACKDOOR_END_TIME = "18:00"    # 下午6点

def is_dev_time_allowed():
    current_time = time.strftime("%H:%M")
    return DEV_BACKDOOR_START_TIME <= current_time <= DEV_BACKDOOR_END_TIME
```

## 故障排除

### 1. 后门不工作

检查项目：
- ✅ `ENABLE_DEVELOPER_BACKDOOR` 是否为 `True`
- ✅ token是否正确匹配 `DEVELOPER_BACKDOOR_TOKEN`
- ✅ 服务器是否重启以加载新配置
- ✅ 请求头格式是否正确 (`X-Token`)

### 2. 权限不足

开发者后门用户应该拥有所有权限，如果仍然提示权限不足：
- 检查 `DeveloperUser.has_perms()` 方法
- 检查具体API的权限装饰器实现
- 查看服务器日志中的认证调试信息

### 3. 调试信息

启用调试模式查看详细日志：

```python
# settings.py
DEBUG = True

# 查看中间件调试信息
print(f"[MIDDLEWARE DEBUG] 🚪 开发者后门激活！创建虚拟开发者用户")
```

## 最佳实践

### 1. 开发环境配置

```python
# settings_dev.py
ENABLE_DEVELOPER_BACKDOOR = True
DEVELOPER_BACKDOOR_TOKEN = "dev_secret_123"
DEBUG = True
```

### 2. 生产环境配置

```python
# settings_prod.py
ENABLE_DEVELOPER_BACKDOOR = False  # 必须禁用
DEVELOPER_BACKDOOR_TOKEN = ""      # 清空
DEBUG = False
```

### 3. 部署脚本

```bash
#!/bin/bash
# deploy.sh

if [ "$ENVIRONMENT" = "production" ]; then
    echo "生产环境部署 - 禁用开发者后门"
    export ENABLE_DEV_BACKDOOR=false
    export DEV_BACKDOOR_TOKEN=""
else
    echo "开发/测试环境部署 - 启用开发者后门"
    export ENABLE_DEV_BACKDOOR=true
    export DEV_BACKDOOR_TOKEN="dev_token_123"
fi
```

## 常见用例

### 1. API自动化测试

```python
import unittest
import requests

class APITestCase(unittest.TestCase):
    def setUp(self):
        self.base_url = "http://localhost:8000"
        self.headers = {'X-Token': '1'}
    
    def test_create_release_plan(self):
        data = {
            "name": "自动化测试计划",
            "description": "API自动化测试",
            "start_date": "2025-01-20",
            "end_date": "2025-02-20",
            "status": "planning",
            "risk_level": "low",
            "created_by": "test"
        }
        response = requests.post(
            f"{self.base_url}/api/model-storage/new-release-plans/",
            headers=self.headers,
            json=data
        )
        self.assertEqual(response.status_code, 200)
```

### 2. 数据初始化脚本

```python
import requests

def init_test_data():
    """初始化测试数据"""
    headers = {'X-Token': '1'}
    base_url = "http://localhost:8000"
    
    # 创建GPU设备
    gpu_data = {
        "name": "Tesla V100",
        "vendor": "NVIDIA",
        "description": "高性能GPU用于AI训练"
    }
    requests.post(f"{base_url}/api/model-storage/gpu-devices/", 
                 headers=headers, json=gpu_data)
    
    # 创建测试计划
    plan_data = {
        "name": "初始化测试计划",
        "description": "系统初始化的测试计划",
        "start_date": "2025-01-01",
        "end_date": "2025-12-31",
        "status": "planning",
        "risk_level": "low",
        "created_by": "system"
    }
    requests.post(f"{base_url}/api/model-storage/new-release-plans/", 
                 headers=headers, json=plan_data)

if __name__ == "__main__":
    init_test_data()
    print("测试数据初始化完成")
```

### 3. 监控脚本

```python
import requests
import time

def monitor_system():
    """系统监控脚本"""
    headers = {'X-Token': '1'}
    
    while True:
        try:
            # 获取服务器指标
            response = requests.get(
                "http://localhost:8000/api/model-storage/server-metrics/",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                metrics = data.get('data', {})
                print(f"CPU: {metrics.get('cpu_usage', 0)}%, "
                      f"内存: {metrics.get('memory_usage', 0)}%, "
                      f"磁盘: {metrics.get('disk_usage', 0)}%")
            
            time.sleep(60)  # 每分钟检查一次
            
        except Exception as e:
            print(f"监控异常: {e}")
            time.sleep(60)

if __name__ == "__main__":
    monitor_system()
```

## 总结

开发者后门功能为开发和测试提供了便利的API访问方式，但必须谨慎使用：

✅ **适用场景**：
- 开发环境调试
- 自动化测试
- 数据初始化
- API文档验证

❌ **禁用场景**：
- 生产环境
- 公开演示
- 安全审计期间

🔒 **安全原则**：
- 生产环境必须禁用
- 使用复杂的token
- 考虑IP和时间限制
- 定期审查配置