# Linux长命令监控服务技术方案

## 需求总结
- **核心功能**: 自动检测运行超过5分钟的命令，完成时推送微信通知
- **监控范围**: 主机进程 + Docker容器内进程
- **通知方式**: 企业微信机器人，包含详细执行信息
- **部署方式**: systemd服务，环境变量配置
- **目标系统**: RHEL/Ubuntu 20/22

## 系统架构设计

### 1. 核心组件
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   进程扫描器     │    │   监控管理器     │    │   通知推送器     │
│  ProcessScanner │────│ MonitorManager  │────│ NotificationSvc │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   /proc 文件系统 │    │   本地存储       │    │   企业微信API    │
│   Docker API    │    │   SQLite/JSON   │    │   Webhook       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 工作流程
1. **进程发现**: 定期扫描系统进程和Docker容器
2. **时长判断**: 识别运行超过5分钟的新进程
3. **状态跟踪**: 监控进程生命周期直到结束
4. **信息收集**: 收集执行时长、退出码、资源使用等
5. **通知推送**: 发送详细完成报告到微信

## 详细技术实现

### 1. 进程监控模块

#### 主机进程监控
```go
type ProcessInfo struct {
    PID         int
    PPID        int
    Command     string
    Args        []string
    StartTime   time.Time
    User        string
    WorkingDir  string
    Status      string
}

// 通过 /proc 文件系统获取进程信息
func ScanHostProcesses() ([]ProcessInfo, error) {
    // 读取 /proc/*/stat, /proc/*/cmdline, /proc/*/status
    // 过滤出运行时间 > 5分钟的进程
}
```

#### Docker容器监控
```go
type ContainerProcess struct {
    ContainerID   string
    ContainerName string
    ProcessInfo   ProcessInfo
}

// 使用 Docker API 监控容器内进程
func ScanDockerProcesses() ([]ContainerProcess, error) {
    // docker exec container_id ps -eo pid,ppid,cmd,etime
    // 或使用 Docker API: /containers/{id}/top
}
```

### 2. 监控管理器

#### 进程生命周期跟踪
```go
type MonitoredProcess struct {
    Info        ProcessInfo
    StartTime   time.Time
    IsContainer bool
    ContainerID string
    Status      string // "running", "completed", "failed"
    ExitCode    *int
    Duration    time.Duration
    CPUUsage    float64
    MemoryUsage int64
}

type MonitorManager struct {
    processes map[int]*MonitoredProcess
    storage   Storage
    notifier  *NotificationService
}

func (m *MonitorManager) StartMonitoring() {
    ticker := time.NewTicker(30 * time.Second)
    for {
        select {
        case <-ticker.C:
            m.scanAndUpdate()
        }
    }
}
```

### 3. 存储模块

#### 本地SQLite存储
```sql
CREATE TABLE monitored_processes (
    id INTEGER PRIMARY KEY,
    pid INTEGER,
    command TEXT,
    args TEXT,
    start_time DATETIME,
    end_time DATETIME,
    duration INTEGER,
    exit_code INTEGER,
    status TEXT,
    is_container BOOLEAN,
    container_id TEXT,
    cpu_usage REAL,
    memory_usage INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 4. 通知推送模块

#### 企业微信机器人集成
```go
type WeChatNotifier struct {
    WebhookURL string
    DefaultContact string
}

type NotificationMessage struct {
    ProcessInfo *MonitoredProcess
    Message     string
    Timestamp   time.Time
}

func (w *WeChatNotifier) SendCompletion(proc *MonitoredProcess) error {
    message := w.formatMessage(proc)
    return w.sendToWeChat(message)
}

func (w *WeChatNotifier) formatMessage(proc *MonitoredProcess) string {
    status := "✅ 成功"
    if proc.ExitCode != nil && *proc.ExitCode != 0 {
        status = "❌ 失败"
    }
    
    return fmt.Sprintf(`
🔔 长命令执行完成通知

%s 命令: %s
⏱️ 执行时长: %s
📊 退出码: %d
💾 内存使用: %s
🖥️ CPU使用: %.2f%%
📍 容器: %s
⏰ 完成时间: %s
`, status, proc.Info.Command, proc.Duration, 
   *proc.ExitCode, formatBytes(proc.MemoryUsage),
   proc.CPUUsage, proc.getContainerInfo(),
   time.Now().Format("2006-01-02 15:04:05"))
}
```

## 配置管理

### 环境变量配置
```bash
# 企业微信配置
WECHAT_WEBHOOK_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx"
WECHAT_DEFAULT_CONTACT="@all"

# 监控配置
MONITOR_THRESHOLD_MINUTES=5
SCAN_INTERVAL_SECONDS=30
STORAGE_PATH="/var/lib/cmdmonitor/data.db"

# Docker配置
DOCKER_SOCKET="/var/run/docker.sock"
MONITOR_DOCKER_ENABLED=true

# 日志配置
LOG_LEVEL="info"
LOG_PATH="/var/log/cmdmonitor.log"
```

## 部署方案

### 1. systemd服务配置
```ini
[Unit]
Description=Command Monitor Service
After=network.target docker.service
Wants=docker.service

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/cmdmonitor
Restart=always
RestartSec=10
EnvironmentFile=/etc/cmdmonitor/config.env

[Install]
WantedBy=multi-user.target
```

### 2. 安装脚本
```bash
#!/bin/bash
# install.sh

# 创建用户和目录
sudo useradd -r -s /bin/false cmdmonitor
sudo mkdir -p /etc/cmdmonitor /var/lib/cmdmonitor /var/log

# 复制二进制文件
sudo cp cmdmonitor /usr/local/bin/
sudo chmod +x /usr/local/bin/cmdmonitor

# 复制配置文件
sudo cp config.env /etc/cmdmonitor/
sudo cp cmdmonitor.service /etc/systemd/system/

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable cmdmonitor
sudo systemctl start cmdmonitor
```

## 项目结构
```
cmdmonitor/
├── cmd/
│   └── main.go                 # 主程序入口
├── internal/
│   ├── monitor/
│   │   ├── scanner.go          # 进程扫描器
│   │   ├── manager.go          # 监控管理器
│   │   └── docker.go           # Docker监控
│   ├── notification/
│   │   ├── wechat.go           # 微信通知
│   │   └── interface.go        # 通知接口
│   ├── storage/
│   │   ├── sqlite.go           # SQLite存储
│   │   └── interface.go        # 存储接口
│   └── config/
│       └── config.go           # 配置管理
├── pkg/
│   └── utils/
│       └── process.go          # 进程工具函数
├── scripts/
│   ├── install.sh              # 安装脚本
│   └── uninstall.sh            # 卸载脚本
├── configs/
│   ├── config.env.example      # 配置示例
│   └── cmdmonitor.service      # systemd服务文件
├── go.mod
├── go.sum
├── Makefile
└── README.md
```

## 下一步计划
1. 创建Go项目结构
2. 实现核心监控逻辑
3. 集成企业微信通知
4. 添加Docker支持
5. 编写安装部署脚本
6. 测试和优化

您觉得这个技术方案如何？我们可以开始实现了吗？
