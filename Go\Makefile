# Makefile for cmdmonitor

# 变量定义
BINARY_NAME=cmdmonitor
MAIN_PATH=cmd/main.go
BUILD_DIR=build
VERSION=$(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -s -w"

# Go相关变量
GOOS=$(shell go env GOOS)
GOARCH=$(shell go env GOARCH)

# 默认目标
.PHONY: all
all: clean build

# 清理构建文件
.PHONY: clean
clean:
	@echo "清理构建文件..."
	@rm -rf $(BUILD_DIR)
	@rm -f $(BINARY_NAME)

# 创建构建目录
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)

# 本地构建
.PHONY: build
build: $(BUILD_DIR)
	@echo "构建 $(BINARY_NAME) for $(GOOS)/$(GOARCH)..."
	@go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)

# Linux构建
.PHONY: build-linux
build-linux: $(BUILD_DIR)
	@echo "交叉编译 $(BINARY_NAME) for linux/amd64..."
	@GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 $(MAIN_PATH)

# 多平台构建
.PHONY: build-all
build-all: $(BUILD_DIR)
	@echo "构建多平台版本..."
	@GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 $(MAIN_PATH)
	@GOOS=linux GOARCH=arm64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-arm64 $(MAIN_PATH)
	@GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 $(MAIN_PATH)
	@GOOS=darwin GOARCH=arm64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-arm64 $(MAIN_PATH)

# 安装依赖
.PHONY: deps
deps:
	@echo "安装依赖..."
	@go mod download
	@go mod tidy

# 运行测试
.PHONY: test
test:
	@echo "运行测试..."
	@go test -v ./...

# 运行测试并生成覆盖率报告
.PHONY: test-coverage
test-coverage:
	@echo "运行测试并生成覆盖率报告..."
	@go test -v -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html

# 代码格式化
.PHONY: fmt
fmt:
	@echo "格式化代码..."
	@go fmt ./...

# 代码检查
.PHONY: vet
vet:
	@echo "代码检查..."
	@go vet ./...

# 运行linter
.PHONY: lint
lint:
	@echo "运行linter..."
	@golangci-lint run

# 本地运行
.PHONY: run
run:
	@echo "本地运行..."
	@go run $(MAIN_PATH)

# 安装到系统
.PHONY: install
install: build-linux
	@echo "安装到系统..."
	@sudo cp $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 /usr/local/bin/$(BINARY_NAME)
	@sudo chmod +x /usr/local/bin/$(BINARY_NAME)
	@sudo cp configs/$(BINARY_NAME).service /etc/systemd/system/
	@sudo mkdir -p /etc/$(BINARY_NAME)
	@sudo cp configs/config.env.example /etc/$(BINARY_NAME)/config.env
	@sudo systemctl daemon-reload
	@echo "安装完成！请编辑 /etc/$(BINARY_NAME)/config.env 配置文件"

# 卸载
.PHONY: uninstall
uninstall:
	@echo "卸载服务..."
	@sudo systemctl stop $(BINARY_NAME) || true
	@sudo systemctl disable $(BINARY_NAME) || true
	@sudo rm -f /etc/systemd/system/$(BINARY_NAME).service
	@sudo rm -f /usr/local/bin/$(BINARY_NAME)
	@sudo rm -rf /etc/$(BINARY_NAME)
	@sudo systemctl daemon-reload
	@echo "卸载完成！"

# 开发环境设置
.PHONY: dev-setup
dev-setup: deps
	@echo "设置开发环境..."
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 显示帮助
.PHONY: help
help:
	@echo "可用的make目标："
	@echo "  build        - 本地构建"
	@echo "  build-linux  - Linux交叉编译"
	@echo "  build-all    - 多平台构建"
	@echo "  clean        - 清理构建文件"
	@echo "  deps         - 安装依赖"
	@echo "  test         - 运行测试"
	@echo "  test-coverage- 测试覆盖率"
	@echo "  fmt          - 格式化代码"
	@echo "  vet          - 代码检查"
	@echo "  lint         - 运行linter"
	@echo "  run          - 本地运行"
	@echo "  install      - 安装到系统"
	@echo "  uninstall    - 卸载服务"
	@echo "  dev-setup    - 设置开发环境"
	@echo "  help         - 显示此帮助"
