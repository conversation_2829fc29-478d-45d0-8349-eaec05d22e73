#!/bin/bash

echo "=== LVM 挂载排查脚本 ==="
echo "时间: $(date)"
echo

echo "1. 检查硬盘分区信息"
echo "===================="
lsblk
echo

echo "2. 检查 LVM 物理卷"
echo "=================="
pvs
pvdisplay
echo

echo "3. 检查 LVM 卷组"
echo "================"
vgs
vgdisplay
echo

echo "4. 检查 LVM 逻辑卷"
echo "=================="
lvs
lvdisplay
echo

echo "5. 检查当前挂载状态"
echo "=================="
mount | grep -E "(sdb|rhel)"
df -h | grep -E "(sdb|rhel)"
echo

echo "6. 检查目标挂载目录"
echo "=================="
ls -la /ww/ 2>/dev/null || echo "/ww 目录不存在"
echo

echo "7. 检查文件系统类型"
echo "=================="
if [ -e /dev/rhel/home ]; then
    echo "检查 /dev/rhel/home:"
    file -s /dev/rhel/home
    blkid /dev/rhel/home
else
    echo "/dev/rhel/home 不存在"
fi

if [ -e /dev/rhel/root ]; then
    echo "检查 /dev/rhel/root:"
    file -s /dev/rhel/root
    blkid /dev/rhel/root
else
    echo "/dev/rhel/root 不存在"
fi
echo

echo "8. 尝试激活 rhel 卷组"
echo "===================="
vgchange -ay rhel
echo "激活后的逻辑卷状态:"
lvs
echo

echo "9. 检查设备映射器"
echo "================"
dmsetup ls
dmsetup status
echo

echo "10. 建议的挂载命令"
echo "================="
echo "创建挂载目录: mkdir -p /ww"
echo
if [ -e /dev/rhel/home ]; then
    echo "挂载 home 分区 (最大): mount /dev/rhel/home /ww"
elif [ -e /dev/rhel/root ]; then
    echo "挂载 root 分区: mount /dev/rhel/root /ww"
else
    echo "未找到可用的逻辑卷，可能需要先激活"
fi
echo

echo "11. 如果挂载失败，尝试文件系统检查"
echo "================================"
if [ -e /dev/rhel/home ]; then
    echo "检查 home 分区: fsck -n /dev/rhel/home"
fi
if [ -e /dev/rhel/root ]; then
    echo "检查 root 分区: fsck -n /dev/rhel/root"
fi
echo

echo "12. 系统日志检查"
echo "==============="
echo "最近的存储相关日志:"
dmesg | grep -i -E "(sdb|lvm|rhel)" | tail -10
echo

echo "=== 排查完成 ==="
echo "如果逻辑卷未激活，请运行: vgchange -ay rhel"
echo "如果需要挂载，请运行: mount /dev/rhel/home /ww"
echo "如果文件系统损坏，可能需要: fsck /dev/rhel/home"
