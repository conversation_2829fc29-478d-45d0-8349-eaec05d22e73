# Technology Stack

## Backend (spug_api/)
- **Framework**: Django 2.2.28
- **Language**: Python 3.11.9+
- **Task Queue**: Funboost (distributed task processing)
- **Scheduler**: APScheduler for cron jobs
- **Database**: SQLite (default), supports MySQL
- **Caching**: Redis
- **WebSockets**: Django Channels with Redis backend
- **Authentication**: LDAP3 support
- **File Processing**: openpyxl for Excel, paramiko for SSH

## Frontend (spug_web/)
- **Framework**: React 16.13.1
- **UI Library**: Ant Design 4.21.5
- **State Management**: MobX 5.15.6
- **Charts**: Biz<PERSON><PERSON>s, Recharts
- **Code Editor**: React Ace (Monaco-based)
- **Terminal**: xterm.js
- **Build Tool**: Create React App with custom overrides
- **Styling**: Less with custom theme

## Infrastructure
- **Logging**: nb_log with structured logging
- **Monitoring**: psutil for system metrics
- **Version Control**: GitPython integration
- **File Transfer**: SMB protocol support
- **Process Management**: Celery integration

## Common Commands

### Development Setup
```bash
# Full environment setup (Windows)
.\start.ps1

# Manual backend setup
cd spug_api
python -m venv venv
.\venv\Scripts\activate
pip install -r requirements.txt
python manage.py runserver

# Manual frontend setup  
cd spug_web
npm install --legacy-peer-deps
npm start
```

### Build & Deploy
```bash
# Frontend production build
cd spug_web
npm run build

# Backend with production settings
cd spug_api
python manage.py runserver --settings=spug.settings_prod
```

### Development Tools
```bash
# Run tests
cd spug_web && npm test
cd spug_api && python manage.py test

# Code formatting (if configured)
# Backend: black, flake8
# Frontend: ESLint (via CRA)
```

## Configuration Notes
- Uses legacy Node.js provider for compatibility
- Custom webpack config via config-overrides.js
- Funboost for distributed task processing
- Redis required for WebSocket and caching features
- Supports both SQLite and MySQL databases