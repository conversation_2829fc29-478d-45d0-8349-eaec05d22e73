# Command Monitor (cmdmonitor)

Linux长命令监控服务 - 自动监控运行超过指定时间的命令，并在完成时发送微信通知。

## 功能特性

- 🔍 **智能监控**: 自动检测运行超过5分钟的长命令
- 📧 **邮件通知**: SMTP邮件推送详细完成报告
- 🐳 **Docker支持**: 监控主机进程和Docker容器内进程
- 💾 **本地存储**: SQLite数据库持久化存储监控历史
- ⚡ **轻量高效**: Go语言开发，资源占用低
- 🔧 **简单部署**: systemd服务，环境变量配置

## 快速开始

### 1. 编译安装

```bash
# 克隆项目
git clone <repository-url>
cd cmdmonitor

# Windows上交叉编译Linux版本
$env:GOOS="linux"; $env:GOARCH="amd64"; go build -o build/cmdmonitor-linux-amd64 cmd/main.go

# 或者使用Makefile
make build-linux

# 上传到Linux服务器并安装
scp build/cmdmonitor-linux-amd64 user@your-server:/tmp/
ssh user@your-server
sudo mv /tmp/cmdmonitor-linux-amd64 /usr/local/bin/cmdmonitor
sudo chmod +x /usr/local/bin/cmdmonitor
```

### 2. 配置

```bash
# 复制配置文件
sudo cp configs/config.env.example /etc/cmdmonitor/config.env

# 编辑配置
sudo nano /etc/cmdmonitor/config.env
```

必须配置的环境变量：
- `EMAIL_USERNAME`: 邮箱用户名
- `EMAIL_PASSWORD`: 邮箱密码或授权码
- `EMAIL_FROM_ADDRESS`: 发件邮箱地址

### 3. 启动服务

```bash
# 启动服务
sudo systemctl start cmdmonitor

# 设置开机自启
sudo systemctl enable cmdmonitor

# 查看状态
sudo systemctl status cmdmonitor

# 查看日志
sudo journalctl -u cmdmonitor -f
```

## 配置说明

### 邮箱SMTP设置

1. 获取邮箱SMTP服务器信息（如QQ邮箱：smtp.qq.com:587）
2. 获取邮箱授权码或应用密码
3. 配置相关环境变量

### 主要配置项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `EMAIL_SMTP_HOST` | smtp.qq.com | SMTP服务器地址 |
| `EMAIL_SMTP_PORT` | 587 | SMTP服务器端口 |
| `EMAIL_DEFAULT_TO` | <EMAIL> | 默认收件人邮箱 |
| `MONITOR_THRESHOLD_MINUTES` | 5 | 监控阈值（分钟） |
| `SCAN_INTERVAL_SECONDS` | 30 | 扫描间隔（秒） |
| `MAX_MONITORED_PROCESSES` | 50 | 最大同时监控进程数 |

## 邮件通知示例

**邮件主题**: [Command Monitor] 长命令执行成功 - train_model.py

**邮件内容**:
```
长命令执行完成通知
==============================

✅ 执行状态: 成功
📋 命令名称: train_model.py
📝 命令参数: --epochs 100 --batch-size 32

详细信息:
--------------------
⏱️  执行时长: 2小时15分钟
📊 退出码: 0
🔢 进程ID: 12345
💾 内存使用: 8.5GB
🖥️  CPU使用: 85.2%
📍 运行环境: 主机进程
👤 执行用户: user1
📂 工作目录: /home/<USER>/ml-project
🕐 开始时间: 2025-01-17 13:15:25
🕑 完成时间: 2025-01-17 15:30:25

==================================================
此邮件由 Command Monitor 自动发送
如有问题，请检查服务配置或联系管理员
```

## 项目结构

```
cmdmonitor/
├── cmd/                    # 主程序入口
├── internal/               # 内部模块
│   ├── monitor/           # 进程监控
│   ├── notification/      # 通知推送
│   ├── storage/          # 数据存储
│   └── config/           # 配置管理
├── pkg/                   # 公共工具
├── scripts/              # 部署脚本
├── configs/              # 配置文件
└── README.md
```

## 开发

### 本地开发

```bash
# 安装依赖
go mod tidy

# 运行
go run cmd/main.go

# 测试
go test ./...
```

### 构建

```bash
# 本地构建
make build

# 交叉编译
make build-linux
```

## 故障排除

### 常见问题

1. **权限不足**: 确保以root权限运行，或配置适当的sudo权限
2. **Docker连接失败**: 检查Docker socket路径和权限
3. **微信通知失败**: 验证Webhook URL和网络连接

### 日志查看

```bash
# 查看服务日志
sudo journalctl -u cmdmonitor -f

# 查看应用日志
sudo tail -f /var/log/cmdmonitor.log
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
