#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.exec.models import TestResult
from libs.utils import json_response
import json

def test_api():
    with open('debug_output.txt', 'w', encoding='utf-8') as f:
        f.write("=== 测试API逻辑 ===\n")

        # 测试数据库连接
        try:
            results = TestResult.objects.all()
            f.write(f"数据库中共有 {len(results)} 条记录\n")
        
            for result in results:
                f.write(f"ID: {result.id}\n")
                f.write(f"plan_name: {repr(result.plan_name)}\n")
                f.write(f"task_name: {repr(result.task_name)}\n")
                f.write(f"source_type: {result.source_type}\n")
                f.write(f"metrics: {result.metrics}\n")
                f.write("---\n")

                # 测试to_dict方法
                try:
                    result_dict = result.to_dict()
                    f.write(f"to_dict() 成功: {json.dumps(result_dict, ensure_ascii=False, indent=2)}\n")
                except Exception as e:
                    f.write(f"to_dict() 失败: {e}\n")
                f.write("=" * 50 + "\n")
            
        except Exception as e:
            f.write(f"数据库查询失败: {e}\n")

        # 测试json_response
        try:
            test_data = [{"id": 1, "name": "测试"}]
            response = json_response(test_data)
            f.write(f"json_response 测试成功: {response}\n")
        except Exception as e:
            f.write(f"json_response 测试失败: {e}\n")

if __name__ == "__main__":
    test_api()
