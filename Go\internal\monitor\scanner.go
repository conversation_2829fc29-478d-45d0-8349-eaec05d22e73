package monitor

import (
	"cmdmonitor/pkg/utils"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// ProcessScanner 进程扫描器
type ProcessScanner struct {
	logger            *logrus.Logger
	thresholdDuration time.Duration
	ignoreProcesses   map[string]bool
	monitorSystem     bool
}

// NewProcessScanner 创建新的进程扫描器
func NewProcessScanner(logger *logrus.Logger, thresholdMinutes int, ignoreList []string, monitorSystem bool) *ProcessScanner {
	ignoreMap := make(map[string]bool)
	for _, proc := range ignoreList {
		ignoreMap[strings.TrimSpace(proc)] = true
	}

	return &ProcessScanner{
		logger:            logger,
		thresholdDuration: time.Duration(thresholdMinutes) * time.Minute,
		ignoreProcesses:   ignoreMap,
		monitorSystem:     monitorSystem,
	}
}

// ScanProcesses 扫描所有符合条件的进程
func (ps *ProcessScanner) ScanProcesses() ([]utils.ProcessInfo, error) {
	ps.logger.Debug("开始扫描进程...")

	var processes []utils.ProcessInfo
	
	// 遍历/proc目录
	err := filepath.WalkDir("/proc", func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return nil // 忽略错误，继续扫描
		}

		// 只处理数字目录（PID目录）
		if !d.IsDir() || !isPIDDir(d.Name()) {
			return nil
		}

		pid, err := strconv.Atoi(d.Name())
		if err != nil {
			return nil
		}

		// 获取进程信息
		procInfo, err := ps.getProcessInfo(pid)
		if err != nil {
			ps.logger.Debugf("获取进程信息失败 PID=%d: %v", pid, err)
			return nil
		}

		// 检查是否符合监控条件
		if ps.shouldMonitor(procInfo) {
			processes = append(processes, *procInfo)
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("扫描/proc目录失败: %v", err)
	}

	ps.logger.Infof("扫描完成，发现 %d 个符合条件的进程", len(processes))
	return processes, nil
}

// getProcessInfo 获取指定PID的进程信息
func (ps *ProcessScanner) getProcessInfo(pid int) (*utils.ProcessInfo, error) {
	// 检查进程是否存在
	if !utils.IsProcessRunning(pid) {
		return nil, fmt.Errorf("进程 %d 不存在", pid)
	}

	// 获取命令行
	command, args, err := utils.GetProcessCmdline(pid)
	if err != nil {
		return nil, fmt.Errorf("获取命令行失败: %v", err)
	}

	// 获取进程状态和用户
	status, user, err := utils.GetProcessStatus(pid)
	if err != nil {
		return nil, fmt.Errorf("获取进程状态失败: %v", err)
	}

	// 获取启动时间
	startTime, err := utils.GetProcessStartTime(pid)
	if err != nil {
		return nil, fmt.Errorf("获取启动时间失败: %v", err)
	}

	// 获取内存使用
	memoryUsage, err := utils.GetProcessMemoryUsage(pid)
	if err != nil {
		ps.logger.Debugf("获取内存使用失败 PID=%d: %v", pid, err)
		memoryUsage = 0
	}

	// 获取CPU时间
	cpuTime, err := utils.GetProcessCPUTime(pid)
	if err != nil {
		ps.logger.Debugf("获取CPU时间失败 PID=%d: %v", pid, err)
		cpuTime = 0
	}

	// 获取工作目录
	workingDir := ps.getWorkingDirectory(pid)

	// 获取父进程ID
	ppid := ps.getParentPID(pid)

	return &utils.ProcessInfo{
		PID:         pid,
		PPID:        ppid,
		Command:     command,
		Args:        args,
		StartTime:   startTime,
		User:        user,
		WorkingDir:  workingDir,
		Status:      status,
		CPUTime:     cpuTime,
		MemoryUsage: memoryUsage,
	}, nil
}

// shouldMonitor 判断是否应该监控该进程
func (ps *ProcessScanner) shouldMonitor(procInfo *utils.ProcessInfo) bool {
	// 检查运行时间是否超过阈值
	runningTime := time.Since(procInfo.StartTime)
	if runningTime < ps.thresholdDuration {
		return false
	}

	// 检查是否在忽略列表中
	commandName := filepath.Base(procInfo.Command)
	if ps.ignoreProcesses[commandName] {
		ps.logger.Debugf("忽略进程: %s (PID=%d)", commandName, procInfo.PID)
		return false
	}

	// 检查是否监控系统进程
	if !ps.monitorSystem {
		uid, err := strconv.Atoi(procInfo.User)
		if err == nil && uid < 1000 {
			ps.logger.Debugf("忽略系统进程: %s (PID=%d, UID=%d)", commandName, procInfo.PID, uid)
			return false
		}
	}

	// 过滤掉一些明显的系统进程
	if ps.isSystemProcess(commandName) {
		ps.logger.Debugf("忽略系统进程: %s (PID=%d)", commandName, procInfo.PID)
		return false
	}

	ps.logger.Debugf("发现长时间运行进程: %s (PID=%d, 运行时间=%s)", 
		commandName, procInfo.PID, utils.FormatDuration(runningTime))
	
	return true
}

// isSystemProcess 判断是否为系统进程
func (ps *ProcessScanner) isSystemProcess(commandName string) bool {
	systemProcesses := []string{
		"systemd", "kthreadd", "ksoftirqd", "migration", "rcu_gp", "rcu_par_gp",
		"kworker", "mm_percpu_wq", "ksoftirqd", "migration", "rcu_", "watchdog",
		"systemd-", "dbus", "NetworkManager", "sshd", "chronyd", "rsyslog",
		"kernel", "init", "swapper", "idle", "irq", "softirq",
	}

	for _, sysProc := range systemProcesses {
		if strings.Contains(commandName, sysProc) {
			return true
		}
	}

	return false
}

// getWorkingDirectory 获取进程工作目录
func (ps *ProcessScanner) getWorkingDirectory(pid int) string {
	cwdPath := fmt.Sprintf("/proc/%d/cwd", pid)
	workingDir, err := os.Readlink(cwdPath)
	if err != nil {
		ps.logger.Debugf("获取工作目录失败 PID=%d: %v", pid, err)
		return ""
	}
	return workingDir
}

// getParentPID 获取父进程ID
func (ps *ProcessScanner) getParentPID(pid int) int {
	statPath := fmt.Sprintf("/proc/%d/stat", pid)
	data, err := os.ReadFile(statPath)
	if err != nil {
		return 0
	}

	fields := strings.Fields(string(data))
	if len(fields) < 4 {
		return 0
	}

	ppid, err := strconv.Atoi(fields[3])
	if err != nil {
		return 0
	}

	return ppid
}

// isPIDDir 判断目录名是否为PID（纯数字）
func isPIDDir(name string) bool {
	_, err := strconv.Atoi(name)
	return err == nil
}

// GetRunningTime 获取进程运行时间
func (ps *ProcessScanner) GetRunningTime(procInfo *utils.ProcessInfo) time.Duration {
	return time.Since(procInfo.StartTime)
}

// RefreshProcessInfo 刷新进程信息
func (ps *ProcessScanner) RefreshProcessInfo(pid int) (*utils.ProcessInfo, error) {
	return ps.getProcessInfo(pid)
}
