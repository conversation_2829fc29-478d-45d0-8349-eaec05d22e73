package storage

import (
	"cmdmonitor/pkg/utils"
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	_ "github.com/mattn/go-sqlite3"
	"github.com/sirupsen/logrus"
)

// SQLiteStorage SQLite存储实现
type SQLiteStorage struct {
	logger *logrus.Logger
	db     *sql.DB
	dbPath string
}

// NewSQLiteStorage 创建新的SQLite存储
func NewSQLiteStorage(logger *logrus.Logger, dbPath string) *SQLiteStorage {
	return &SQLiteStorage{
		logger: logger,
		dbPath: dbPath,
	}
}

// Initialize 初始化存储
func (s *SQLiteStorage) Initialize() error {
	s.logger.Infof("初始化SQLite数据库: %s", s.dbPath)

	// 确保目录存在
	dir := filepath.Dir(s.dbPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建数据库目录失败: %v", err)
	}

	// 打开数据库连接
	db, err := sql.Open("sqlite3", s.dbPath)
	if err != nil {
		return fmt.Errorf("打开数据库失败: %v", err)
	}

	s.db = db

	// 创建表
	if err := s.createTables(); err != nil {
		return fmt.Errorf("创建表失败: %v", err)
	}

	s.logger.Info("SQLite数据库初始化完成")
	return nil
}

// createTables 创建数据库表
func (s *SQLiteStorage) createTables() error {
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS monitored_processes (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		pid INTEGER NOT NULL,
		command TEXT NOT NULL,
		args TEXT,
		start_time DATETIME NOT NULL,
		end_time DATETIME,
		duration INTEGER,
		exit_code INTEGER,
		status TEXT NOT NULL DEFAULT 'running',
		is_container BOOLEAN NOT NULL DEFAULT 0,
		container_id TEXT,
		cpu_usage REAL DEFAULT 0,
		memory_usage INTEGER DEFAULT 0,
		user_name TEXT,
		working_dir TEXT,
		last_seen DATETIME,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);

	CREATE INDEX IF NOT EXISTS idx_pid ON monitored_processes(pid);
	CREATE INDEX IF NOT EXISTS idx_status ON monitored_processes(status);
	CREATE INDEX IF NOT EXISTS idx_start_time ON monitored_processes(start_time);
	CREATE INDEX IF NOT EXISTS idx_is_container ON monitored_processes(is_container);
	CREATE INDEX IF NOT EXISTS idx_command ON monitored_processes(command);
	`

	_, err := s.db.Exec(createTableSQL)
	return err
}

// SaveMonitoredProcess 保存监控进程
func (s *SQLiteStorage) SaveMonitoredProcess(process *utils.MonitoredProcess) error {
	s.logger.Debugf("保存监控进程: PID=%d, Command=%s", process.Info.PID, process.Info.Command)

	argsJSON, _ := json.Marshal(process.Info.Args)

	insertSQL := `
	INSERT INTO monitored_processes (
		pid, command, args, start_time, status, is_container, container_id,
		cpu_usage, memory_usage, user_name, working_dir, last_seen
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := s.db.Exec(insertSQL,
		process.Info.PID,
		process.Info.Command,
		string(argsJSON),
		process.StartTime,
		process.Status,
		process.IsContainer,
		process.ContainerID,
		process.CPUUsage,
		process.MemoryUsage,
		process.Info.User,
		process.Info.WorkingDir,
		process.LastSeen,
	)

	if err != nil {
		return fmt.Errorf("保存进程失败: %v", err)
	}

	return nil
}

// UpdateMonitoredProcess 更新监控进程
func (s *SQLiteStorage) UpdateMonitoredProcess(process *utils.MonitoredProcess) error {
	s.logger.Debugf("更新监控进程: PID=%d, Status=%s", process.Info.PID, process.Status)

	updateSQL := `
	UPDATE monitored_processes SET
		status = ?,
		end_time = ?,
		duration = ?,
		exit_code = ?,
		cpu_usage = ?,
		memory_usage = ?,
		last_seen = ?,
		updated_at = CURRENT_TIMESTAMP
	WHERE pid = ?
	`

	var endTime *time.Time
	var duration *int64

	if process.Status == "completed" {
		now := time.Now()
		endTime = &now
		durationMs := int64(process.Duration / time.Millisecond)
		duration = &durationMs
	}

	_, err := s.db.Exec(updateSQL,
		process.Status,
		endTime,
		duration,
		process.ExitCode,
		process.CPUUsage,
		process.MemoryUsage,
		process.LastSeen,
		process.Info.PID,
	)

	if err != nil {
		return fmt.Errorf("更新进程失败: %v", err)
	}

	return nil
}

// GetMonitoredProcess 获取监控进程
func (s *SQLiteStorage) GetMonitoredProcess(pid int) (*utils.MonitoredProcess, error) {
	selectSQL := `
	SELECT pid, command, args, start_time, end_time, duration, exit_code, status,
		   is_container, container_id, cpu_usage, memory_usage, user_name, 
		   working_dir, last_seen
	FROM monitored_processes 
	WHERE pid = ?
	`

	row := s.db.QueryRow(selectSQL, pid)

	process, err := s.scanProcess(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("进程 %d 未找到", pid)
		}
		return nil, fmt.Errorf("查询进程失败: %v", err)
	}

	return process, nil
}

// GetAllMonitoredProcesses 获取所有监控进程
func (s *SQLiteStorage) GetAllMonitoredProcesses() ([]*utils.MonitoredProcess, error) {
	selectSQL := `
	SELECT pid, command, args, start_time, end_time, duration, exit_code, status,
		   is_container, container_id, cpu_usage, memory_usage, user_name, 
		   working_dir, last_seen
	FROM monitored_processes 
	ORDER BY start_time DESC
	`

	rows, err := s.db.Query(selectSQL)
	if err != nil {
		return nil, fmt.Errorf("查询所有进程失败: %v", err)
	}
	defer rows.Close()

	var processes []*utils.MonitoredProcess
	for rows.Next() {
		process, err := s.scanProcess(rows)
		if err != nil {
			s.logger.Errorf("扫描进程记录失败: %v", err)
			continue
		}
		processes = append(processes, process)
	}

	return processes, nil
}

// GetProcessesByTimeRange 获取指定时间范围内的进程
func (s *SQLiteStorage) GetProcessesByTimeRange(startTime, endTime time.Time) ([]*utils.MonitoredProcess, error) {
	selectSQL := `
	SELECT pid, command, args, start_time, end_time, duration, exit_code, status,
		   is_container, container_id, cpu_usage, memory_usage, user_name, 
		   working_dir, last_seen
	FROM monitored_processes 
	WHERE start_time BETWEEN ? AND ?
	ORDER BY start_time DESC
	`

	rows, err := s.db.Query(selectSQL, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("按时间范围查询进程失败: %v", err)
	}
	defer rows.Close()

	var processes []*utils.MonitoredProcess
	for rows.Next() {
		process, err := s.scanProcess(rows)
		if err != nil {
			s.logger.Errorf("扫描进程记录失败: %v", err)
			continue
		}
		processes = append(processes, process)
	}

	return processes, nil
}

// GetCompletedProcesses 获取已完成的进程
func (s *SQLiteStorage) GetCompletedProcesses(limit int) ([]*utils.MonitoredProcess, error) {
	selectSQL := `
	SELECT pid, command, args, start_time, end_time, duration, exit_code, status,
		   is_container, container_id, cpu_usage, memory_usage, user_name, 
		   working_dir, last_seen
	FROM monitored_processes 
	WHERE status = 'completed'
	ORDER BY end_time DESC
	LIMIT ?
	`

	rows, err := s.db.Query(selectSQL, limit)
	if err != nil {
		return nil, fmt.Errorf("查询已完成进程失败: %v", err)
	}
	defer rows.Close()

	var processes []*utils.MonitoredProcess
	for rows.Next() {
		process, err := s.scanProcess(rows)
		if err != nil {
			s.logger.Errorf("扫描进程记录失败: %v", err)
			continue
		}
		processes = append(processes, process)
	}

	return processes, nil
}

// DeleteProcess 删除进程记录
func (s *SQLiteStorage) DeleteProcess(pid int) error {
	s.logger.Debugf("删除进程记录: PID=%d", pid)

	deleteSQL := `DELETE FROM monitored_processes WHERE pid = ?`

	result, err := s.db.Exec(deleteSQL, pid)
	if err != nil {
		return fmt.Errorf("删除进程失败: %v", err)
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("进程 %d 未找到", pid)
	}

	return nil
}

// CleanupOldRecords 清理旧记录
func (s *SQLiteStorage) CleanupOldRecords(olderThan time.Time) error {
	s.logger.Infof("清理早于 %s 的记录", olderThan.Format("2006-01-02 15:04:05"))

	deleteSQL := `
	DELETE FROM monitored_processes
	WHERE status = 'completed' AND end_time < ?
	`

	result, err := s.db.Exec(deleteSQL, olderThan)
	if err != nil {
		return fmt.Errorf("清理旧记录失败: %v", err)
	}

	rowsAffected, _ := result.RowsAffected()
	s.logger.Infof("清理了 %d 条旧记录", rowsAffected)

	return nil
}

// GetStatistics 获取统计信息
func (s *SQLiteStorage) GetStatistics() (*StorageStatistics, error) {
	stats := &StorageStatistics{}

	// 总进程数
	err := s.db.QueryRow("SELECT COUNT(*) FROM monitored_processes").Scan(&stats.TotalProcesses)
	if err != nil {
		return nil, fmt.Errorf("查询总进程数失败: %v", err)
	}

	// 运行中进程数
	err = s.db.QueryRow("SELECT COUNT(*) FROM monitored_processes WHERE status = 'running'").Scan(&stats.RunningProcesses)
	if err != nil {
		return nil, fmt.Errorf("查询运行中进程数失败: %v", err)
	}

	// 已完成进程数
	err = s.db.QueryRow("SELECT COUNT(*) FROM monitored_processes WHERE status = 'completed'").Scan(&stats.CompletedProcesses)
	if err != nil {
		return nil, fmt.Errorf("查询已完成进程数失败: %v", err)
	}

	// 失败进程数
	err = s.db.QueryRow("SELECT COUNT(*) FROM monitored_processes WHERE status = 'completed' AND exit_code != 0").Scan(&stats.FailedProcesses)
	if err != nil {
		return nil, fmt.Errorf("查询失败进程数失败: %v", err)
	}

	// 容器进程数
	err = s.db.QueryRow("SELECT COUNT(*) FROM monitored_processes WHERE is_container = 1").Scan(&stats.ContainerProcesses)
	if err != nil {
		return nil, fmt.Errorf("查询容器进程数失败: %v", err)
	}

	// 主机进程数
	err = s.db.QueryRow("SELECT COUNT(*) FROM monitored_processes WHERE is_container = 0").Scan(&stats.HostProcesses)
	if err != nil {
		return nil, fmt.Errorf("查询主机进程数失败: %v", err)
	}

	return stats, nil
}

// Close 关闭存储
func (s *SQLiteStorage) Close() error {
	if s.db != nil {
		s.logger.Info("关闭SQLite数据库连接")
		return s.db.Close()
	}
	return nil
}

// scanProcess 扫描进程记录
func (s *SQLiteStorage) scanProcess(scanner interface{}) (*utils.MonitoredProcess, error) {
	var process utils.MonitoredProcess
	var argsJSON string
	var endTime sql.NullTime
	var duration sql.NullInt64
	var exitCode sql.NullInt64
	var containerID sql.NullString
	var userName sql.NullString
	var workingDir sql.NullString
	var lastSeen sql.NullTime

	var err error

	// 根据scanner类型进行扫描
	switch s := scanner.(type) {
	case *sql.Row:
		err = s.Scan(
			&process.Info.PID,
			&process.Info.Command,
			&argsJSON,
			&process.StartTime,
			&endTime,
			&duration,
			&exitCode,
			&process.Status,
			&process.IsContainer,
			&containerID,
			&process.CPUUsage,
			&process.MemoryUsage,
			&userName,
			&workingDir,
			&lastSeen,
		)
	case *sql.Rows:
		err = s.Scan(
			&process.Info.PID,
			&process.Info.Command,
			&argsJSON,
			&process.StartTime,
			&endTime,
			&duration,
			&exitCode,
			&process.Status,
			&process.IsContainer,
			&containerID,
			&process.CPUUsage,
			&process.MemoryUsage,
			&userName,
			&workingDir,
			&lastSeen,
		)
	default:
		return nil, fmt.Errorf("不支持的scanner类型")
	}

	if err != nil {
		return nil, err
	}

	// 解析JSON参数
	if argsJSON != "" {
		json.Unmarshal([]byte(argsJSON), &process.Info.Args)
	}

	// 处理可空字段
	if exitCode.Valid {
		code := int(exitCode.Int64)
		process.ExitCode = &code
	}

	if duration.Valid {
		process.Duration = time.Duration(duration.Int64) * time.Millisecond
	}

	if containerID.Valid {
		process.ContainerID = containerID.String
	}

	if userName.Valid {
		process.Info.User = userName.String
	}

	if workingDir.Valid {
		process.Info.WorkingDir = workingDir.String
	}

	if lastSeen.Valid {
		process.LastSeen = lastSeen.Time
	}

	return &process, nil
}
