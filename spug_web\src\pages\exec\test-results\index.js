import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import { Card, Table, Button, Space, Input, Select, DatePicker, Tooltip, Statistic, Row, Col, message, Modal, Form } from 'antd';
import { LineChartOutlined, Bar<PERSON><PERSON>Outlined, ReloadOutlined, EyeOutlined, EditOutlined, DeleteOutlined, CloudDownloadOutlined } from '@ant-design/icons';
import http from 'libs/http';
import store from './store';
import DetailModal from './DetailModal';
import CompareModal from './CompareModal';
import ChartView from './ChartView';
import RemoteLogExtraction from './RemoteLogExtraction';
import styles from './index.module.less';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

export default observer(() => {
  const [detailVisible, setDetailVisible] = useState(false);
  const [compareVisible, setCompareVisible] = useState(false);
  const [chartVisible, setChartVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [remoteExtractionVisible, setRemoteExtractionVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [selectedRows, setSelectedRows] = useState([]);
  const [editingRecord, setEditingRecord] = useState(null);
  const [editForm] = Form.useForm();
  const [taskList, setTaskList] = useState([]);
  const [filters, setFilters] = useState({
    planName: '',
    taskName: '',
    dateRange: null
  });

  useEffect(() => {
    console.log('[DEBUG] Component mounted, calling fetchResults');
    store.fetchResults();
    fetchTaskList();
  }, []);

  // 获取任务列表
  const fetchTaskList = async () => {
    try {
      const response = await http.get('/api/config/task/');
      setTaskList(response || []);
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败');
    }
  };

  // 筛选处理
  const handleFilter = () => {
    store.setFilters(filters);
    store.fetchResults();
  };

  // 重置筛选
  const handleReset = () => {
    setFilters({
      planName: '',
      taskName: '',
      dateRange: null
    });
    store.setFilters({});
    store.fetchResults();
  };

  // 查看详情
  const handleViewDetail = (record) => {
    setSelectedRecord(record);
    setDetailVisible(true);
  };

  // 编辑结果
  const handleEdit = (record) => {
    setEditingRecord(record);
    editForm.setFieldsValue({
      plan_name: record.plan_name,
      task_name: record.task_name || '',
      confirmed_metrics: record.confirmed_metrics
    });
    setEditVisible(true);
  };

  // 保存编辑
  const handleSaveEdit = async () => {
    try {
      const values = await editForm.validateFields();
      await store.updateResult(editingRecord.id, values);
      message.success('更新成功');
      setEditVisible(false);
      setEditingRecord(null);
      editForm.resetFields();
    } catch (error) {
      if (error.errorFields) {
        message.error('请填写完整信息');
      } else {
        message.error('更新失败');
      }
    }
  };

  // 结果对比
  const handleCompare = () => {
    if (selectedRows.length < 2) {
      message.warning('请至少选择2个结果进行对比');
      return;
    }
    if (selectedRows.length > 5) {
      message.warning('最多只能选择5个结果进行对比');
      return;
    }
    setCompareVisible(true);
  };

  // 删除结果
  const handleDelete = (id) => {
    store.deleteResult(id);
  };

  // 图表分析
  const handleChartView = () => {
    if (store.results.length === 0) {
      message.warning('暂无数据可用于图表分析');
      return;
    }
    setChartVisible(true);
  };

  // 远程日志提取
  const handleRemoteExtraction = () => {
    setRemoteExtractionVisible(true);
  };

  // 表格列配置
  const columns = [
    {
      title: '测试计划',
      dataIndex: 'plan_name',
      key: 'plan_name',
      width: 100,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <span className={styles.planName}>{text}</span>
        </Tooltip>
      )
    },
    {
      title: '关联任务',
      dataIndex: 'task_name',
      key: 'task_name',
      width: 100,
      ellipsis: true,
      render: (text) => text ? (
        <a 
          href="/config/task" 
          target="_blank" 
          rel="noopener noreferrer"
          style={{ color: '#1890ff', textDecoration: 'none' }}
          onMouseEnter={(e) => e.target.style.textDecoration = 'underline'}
          onMouseLeave={(e) => e.target.style.textDecoration = 'none'}
        >
          {text}
        </a>
      ) : (
        <span style={{ color: '#999' }}>未关联</span>
      )
    },
    {
      title: '指标统计',
      key: 'metrics_stats',
      width: 60,
      render: (_, record) => (
        <div className={styles.metricsStats}>
          <div>总数: <span className={styles.totalCount}>{record.total_metrics}</span></div>
          <div>确认: <span className={styles.confirmedCount}>{record.confirmed_metrics}</span></div>
        </div>
      )
    },
    // {
    //   title: '成功率',
    //   key: 'success_rate',
    //   width: 120,
    //   render: (_, record) => {
    //     const rate = record.success_rate || 0;
    //     const color = rate >= 80 ? '#52c41a' : rate >= 60 ? '#faad14' : '#ff4d4f';
    //     return (
    //       <Progress
    //         percent={rate}
    //         size="small"
    //         strokeColor={color}
    //         format={(percent) => `${percent}%`}
    //       />
    //     );
    //   }
    // },
    // {
    //   title: 'AI置信度',
    //   dataIndex: 'ai_confidence',
    //   key: 'ai_confidence',
    //   width: 100,
    //   render: (confidence) => {
    //     const value = Math.round((confidence || 0) * 100);
    //     const color = value >= 80 ? 'green' : value >= 60 ? 'orange' : 'red';
    //     return <Tag color={color}>{value}%</Tag>;
    //   }
    // },
    {
      title: '指标名称',
      dataIndex: 'metric_names',
      key: 'metric_names',
      width: 200,
      render: (metricNames, record) => {

        
        // 检查数据
        if (!metricNames || !Array.isArray(metricNames) || metricNames.length === 0) {
          return <span style={{ color: '#999' }}>无指标</span>;
        }
        
        // 显示前3个指标名称
        const display = metricNames.slice(0, 3).join(', ');
        const more = metricNames.length > 3 ? ` 等${metricNames.length}个` : '';
        
        return (
          <span title={metricNames.join(', ')}>
            {display}{more}
          </span>
        );
      }
    },
    {
      title: '提取时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (text) => text
    },
    {
      title: '操作',
      key: 'actions',
      width: 160,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys: selectedRows.map(r => r.id),
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRows(selectedRows);
    },
    getCheckboxProps: (record) => ({
      name: record.plan_name,
    }),
  };

  return (
    <div className={styles.container}>
      {/* 顶部统计卡片 */}
      <Row gutter={16} className={styles.statsRow}>
        <Col span={6}>
          <Card>
            <Statistic title="总结果数" value={store.results.length} prefix={<LineChartOutlined />} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="平均成功率" 
              value={store.avgSuccessRate} 
              suffix="%" 
              precision={1}
              valueStyle={{ color: store.avgSuccessRate >= 80 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="平均AI置信度" 
              value={store.avgAIConfidence} 
              suffix="%" 
              precision={1}
              valueStyle={{ color: store.avgAIConfidence >= 80 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="已确认指标" value={store.totalConfirmedMetrics} prefix={<BarChartOutlined />} />
          </Card>
        </Col>
      </Row>

      {/* 筛选区域 */}
      <Card className={styles.filterCard}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Search
              placeholder="测试计划名称"
              value={filters.planName}
              onChange={(e) => setFilters({...filters, planName: e.target.value})}
              onSearch={handleFilter}
            />
          </Col>
          <Col span={6}>
            <Search
              placeholder="任务名称"
              value={filters.taskName}
              onChange={(e) => setFilters({...filters, taskName: e.target.value})}
              onSearch={handleFilter}
            />
          </Col>
          <Col span={8}>
            <RangePicker
              value={filters.dateRange}
              onChange={(dates) => setFilters({...filters, dateRange: dates})}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Space>
              <Button type="primary" onClick={handleFilter}>筛选</Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 操作工具栏 */}
      <Card className={styles.toolbarCard}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<CloudDownloadOutlined />}
                onClick={handleRemoteExtraction}
              >
                远程日志提取
              </Button>
              <Button
                type="primary"
                icon={<BarChartOutlined />}
                onClick={handleCompare}
                disabled={selectedRows.length < 2}
              >
                结果对比 ({selectedRows.length})
              </Button>
              <Button
                icon={<LineChartOutlined />}
                onClick={handleChartView}
                disabled={store.results.length === 0}
              >
                图表分析
              </Button>
            </Space>
          </Col>
          <Col>
            <Button icon={<ReloadOutlined />} onClick={() => store.fetchResults()}>
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 结果表格 */}
      <Card>
        {console.log('[DEBUG] Rendering table with results:', store.results, 'loading:', store.loading)}
        {console.log('[DEBUG] Results length:', store.results.length)}
        {console.log('[DEBUG] Results array:', JSON.stringify(store.results.slice(), null, 2))}
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={store.results}
          rowKey={(record) => record.id}
          loading={store.loading}
          pagination={{
            total: store.results.length,
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 详情弹窗 */}
      <DetailModal
        visible={detailVisible}
        onCancel={() => setDetailVisible(false)}
        record={selectedRecord}
      />

      {/* 对比弹窗 */}
      <CompareModal
        visible={compareVisible}
        onCancel={() => setCompareVisible(false)}
        records={selectedRows}
      />

      {/* 图表分析弹窗 */}
      <ChartView
        visible={chartVisible}
        onCancel={() => setChartVisible(false)}
        data={store.results}
      />

      {/* 编辑弹窗 */}
      <Modal
        title="编辑测试结果"
        visible={editVisible}
        onOk={handleSaveEdit}
        onCancel={() => {
          setEditVisible(false);
          setEditingRecord(null);
          editForm.resetFields();
        }}
        width={600}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={editForm}
          layout="vertical"
          preserve={false}
        >
          <Form.Item
            name="plan_name"
            label="测试计划名称"
            rules={[{ required: true, message: '请输入测试计划名称' }]}
          >
            <Input placeholder="请输入测试计划名称" />
          </Form.Item>
          
          <Form.Item
            name="task_name"
            label="关联任务名称"
          >
            <Select
              placeholder="请选择关联任务（可选）"
              allowClear
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().includes(input.toLowerCase())
              }
            >
              {taskList.map(task => (
                <Option key={task.id} value={task.name}>
                  {task.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="confirmed_metrics"
            label="已确认指标数量"
            rules={[
              { required: true, message: '请输入已确认指标数量' },
              { 
                type: 'number', 
                min: 0, 
                transform: (value) => Number(value),
                message: '已确认指标数量必须是非负数' 
              }
            ]}
          >
            <Input 
              type="number" 
              min={0} 
              placeholder="请输入已确认指标数量" 
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 远程日志提取弹窗 */}
      <RemoteLogExtraction
        visible={remoteExtractionVisible}
        onCancel={() => setRemoteExtractionVisible(false)}
        onSuccess={() => {
          store.fetchResults();
          setRemoteExtractionVisible(false);
        }}
      />
    </div>
  );
}); 